Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C1_Init) for MX_I2C1_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C2_Init) for MX_I2C2_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM4_Init) for MX_TIM4_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART5_Init) for MX_UART5_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART4_Init) for MX_UART4_Init
    main.o(.text.main) refers to schedule.o(.text.schedule_init) for schedule_init
    main.o(.text.main) refers to mypid.o(.text.PID_INIT) for PID_INIT
    main.o(.text.main) refers to uart_bsp.o(.bss..L_MergedGlobals) for ringbuffer_y
    main.o(.text.main) refers to uart_bsp.o(.bss.ringbuffer_pool_y) for ringbuffer_pool_y
    main.o(.text.main) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(.text.main) refers to uart_bsp.o(.bss.ringbuffer_pool_x) for ringbuffer_pool_x
    main.o(.text.main) refers to uart_bsp.o(.bss.ringbuffer_pool_pi) for ringbuffer_pool_pi
    main.o(.text.main) refers to step_motor_bsp.o(.text.Step_Motor_Init) for Step_Motor_Init
    main.o(.text.main) refers to usart.o(.bss.huart4) for huart4
    main.o(.text.main) refers to emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) for Emm_V5_Reset_CurPos_To_Zero
    main.o(.text.main) refers to usart.o(.bss.huart2) for huart2
    main.o(.text.main) refers to uart_bsp.o(.text.save_initial_position) for save_initial_position
    main.o(.text.main) refers to step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) for Step_Motor_Set_Speed_my
    main.o(.text.main) refers to schedule.o(.text.schedule_run) for schedule_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C1_Init) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C1_Init) refers to i2c.o(.text.MX_I2C1_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C2_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    i2c.o(.text.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C2_Init) refers to i2c.o(.text.MX_I2C2_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM4_Init) refers to tim.o(.bss.htim4) for htim4
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM4_Init) refers to tim.o(.text.MX_TIM4_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.motor_x_buf) for motor_x_buf
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_bsp.o(.bss..L_MergedGlobals) for ringbuffer_x
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.motor_y_buf) for motor_y_buf
    usart.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.pi_rx_buf) for pi_rx_buf
    usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.huart4) for huart4
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.motor_y_buf) for motor_y_buf
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    usart.o(.ARM.exidx.text.MX_UART4_Init) refers to usart.o(.text.MX_UART4_Init) for [Anonymous Symbol]
    usart.o(.text.MX_UART5_Init) refers to usart.o(.bss.huart5) for huart5
    usart.o(.text.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART5_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART5_Init) refers to usart.o(.text.MX_UART5_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.motor_x_buf) for motor_x_buf
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART3_UART_Init) refers to usart.o(.bss.huart3) for huart3
    usart.o(.text.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART3_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART3_UART_Init) refers to usart.o(.text.MX_USART3_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART6_UART_Init) refers to usart.o(.bss.huart6) for huart6
    usart.o(.text.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART6_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.MX_USART6_UART_Init) refers to usart.o(.bss.pi_rx_buf) for pi_rx_buf
    usart.o(.text.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(.text.MX_USART6_UART_Init) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    usart.o(.ARM.exidx.text.MX_USART6_UART_Init) refers to usart.o(.text.MX_USART6_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.motor_x_buf) for motor_x_buf
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to usart.o(.bss.huart3) for huart3
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.huart4) for huart4
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.motor_y_buf) for motor_y_buf
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.hdma_uart4_rx) for hdma_uart4_rx
    stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to usart.o(.bss.huart6) for huart6
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to usart.o(.bss.pi_rx_buf) for pi_rx_buf
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to usart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    oled.o(.text.OLED_Write_cmd) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Write_cmd) refers to oled.o(.text.OLED_Write_cmd) for [Anonymous Symbol]
    oled.o(.text.OLED_Write_data) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Write_data) refers to oled.o(.text.OLED_Write_data) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowPic) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowPic) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_ShowPic) refers to oled.o(.text.OLED_ShowPic) for [Anonymous Symbol]
    oled.o(.text.OLED_Set_Position) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Set_Position) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Set_Position) refers to oled.o(.text.OLED_Set_Position) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHanzi) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowHanzi) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowHanzi) refers to oled.o(.rodata.Hzk) for Hzk
    oled.o(.ARM.exidx.text.OLED_ShowHanzi) refers to oled.o(.text.OLED_ShowHanzi) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHzbig) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowHzbig) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowHzbig) refers to oled.o(.rodata.Hzb) for Hzb
    oled.o(.ARM.exidx.text.OLED_ShowHzbig) refers to oled.o(.text.OLED_ShowHzbig) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloat) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowFloat) refers to oled.o(.text.OLED_ShowFloat) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_ShowChar) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F8X16) for F8X16
    oled.o(.text.OLED_ShowChar) refers to oled.o(.rodata.F6X8) for F6X8
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowStr) refers to oled.o(.text.OLED_ShowChar) for OLED_ShowChar
    oled.o(.ARM.exidx.text.OLED_ShowStr) refers to oled.o(.text.OLED_ShowStr) for [Anonymous Symbol]
    oled.o(.text.OLED_Allfill) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Allfill) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Allfill) refers to oled.o(.text.OLED_Allfill) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Clear) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_On) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Display_On) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Display_On) refers to oled.o(.text.OLED_Display_On) for [Anonymous Symbol]
    oled.o(.text.OLED_Display_Off) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Display_Off) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.ARM.exidx.text.OLED_Display_Off) refers to oled.o(.text.OLED_Display_Off) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    oled.o(.text.OLED_Init) refers to oled.o(.data.initcmd1) for initcmd1
    oled.o(.text.OLED_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    oled.o(.text.OLED_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_Clear) for OLED_Clear
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_init) refers to ringbuffer.o(.rodata.str1.1) for .L.str.1
    ringbuffer.o(.text.rt_ringbuffer_init) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_put) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_put) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put) refers to ringbuffer.o(.text.rt_ringbuffer_put) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.text.rt_ringbuffer_put_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(.text.rt_ringbuffer_get) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_get) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get) refers to ringbuffer.o(.text.rt_ringbuffer_get) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_peek) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_peek) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek) refers to ringbuffer.o(.text.rt_ringbuffer_peek) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_putchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.text.rt_ringbuffer_putchar) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_putchar_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.text.rt_ringbuffer_putchar_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_getchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.text.rt_ringbuffer_getchar) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_reset) refers to ringbuffer.o(.rodata.str1.1) for .L.str.3
    ringbuffer.o(.text.rt_ringbuffer_reset) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for [Anonymous Symbol]
    schedule.o(.text.schedule_init) refers to schedule.o(.bss.task_num) for task_num
    schedule.o(.ARM.exidx.text.schedule_init) refers to schedule.o(.text.schedule_init) for [Anonymous Symbol]
    schedule.o(.text.schedule_run) refers to schedule.o(.bss.task_num) for task_num
    schedule.o(.text.schedule_run) refers to schedule.o(.data.schedule_task) for schedule_task
    schedule.o(.text.schedule_run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    schedule.o(.ARM.exidx.text.schedule_run) refers to schedule.o(.text.schedule_run) for [Anonymous Symbol]
    schedule.o(.data.schedule_task) refers to uart_bsp.o(.text.uart_proc) for uart_proc
    oled_bsp.o(.text.Oled_Printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_bsp.o(.text.Oled_Printf) refers to oled.o(.text.OLED_ShowStr) for OLED_ShowStr
    oled_bsp.o(.ARM.exidx.text.Oled_Printf) refers to oled_bsp.o(.text.Oled_Printf) for [Anonymous Symbol]
    oled_bsp.o(.ARM.exidx.text.oled_proc) refers to oled_bsp.o(.text.oled_proc) for [Anonymous Symbol]
    key_bsp.o(.text.key_read) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_bsp.o(.ARM.exidx.text.key_read) refers to key_bsp.o(.text.key_read) for [Anonymous Symbol]
    key_bsp.o(.text.key_proc) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_bsp.o(.text.key_proc) refers to key_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    key_bsp.o(.ARM.exidx.text.key_proc) refers to key_bsp.o(.text.key_proc) for [Anonymous Symbol]
    uart_bsp.o(.text) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.my_printf) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_bsp.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_bsp.o(.ARM.exidx.text.my_printf) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.my_printf) refers to uart_bsp.o(.text.my_printf) for [Anonymous Symbol]
    uart_bsp.o(.text.check_motor_angle_limits) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.check_motor_angle_limits) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.check_motor_angle_limits) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.check_motor_angle_limits) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.check_motor_angle_limits) refers to usart.o(.bss.huart2) for huart2
    uart_bsp.o(.text.check_motor_angle_limits) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    uart_bsp.o(.text.check_motor_angle_limits) refers to usart.o(.bss.huart4) for huart4
    uart_bsp.o(.ARM.exidx.text.check_motor_angle_limits) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.check_motor_angle_limits) refers to uart_bsp.o(.text.check_motor_angle_limits) for [Anonymous Symbol]
    uart_bsp.o(.text.calc_motor_angle) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.calc_motor_angle) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.calc_motor_angle) refers to uart_bsp.o(.text.calc_motor_angle) for [Anonymous Symbol]
    uart_bsp.o(.text.calc_relative_angle) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.calc_relative_angle) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.calc_relative_angle) refers to uart_bsp.o(.text.calc_relative_angle) for [Anonymous Symbol]
    uart_bsp.o(.text.parse_x_motor_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.parse_x_motor_data) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.parse_x_motor_data) refers to uart_bsp.o(.rodata.str1.1) for .L.str.6
    uart_bsp.o(.text.parse_x_motor_data) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.parse_x_motor_data) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.parse_x_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    uart_bsp.o(.ARM.exidx.text.parse_x_motor_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.parse_x_motor_data) refers to uart_bsp.o(.text.parse_x_motor_data) for [Anonymous Symbol]
    uart_bsp.o(.text.parse_y_motor_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.parse_y_motor_data) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.parse_y_motor_data) refers to uart_bsp.o(.rodata.str1.1) for .L.str.22
    uart_bsp.o(.text.parse_y_motor_data) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.parse_y_motor_data) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.parse_y_motor_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    uart_bsp.o(.ARM.exidx.text.parse_y_motor_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.parse_y_motor_data) refers to uart_bsp.o(.text.parse_y_motor_data) for [Anonymous Symbol]
    uart_bsp.o(.text.process_reset_command) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.process_reset_command) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.process_reset_command) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.process_reset_command) refers to uart_bsp.o(.rodata.str1.1) for .L.str.35
    uart_bsp.o(.text.process_reset_command) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.process_reset_command) refers to usart.o(.bss.huart2) for huart2
    uart_bsp.o(.text.process_reset_command) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    uart_bsp.o(.text.process_reset_command) refers to usart.o(.bss.huart4) for huart4
    uart_bsp.o(.ARM.exidx.text.process_reset_command) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.process_reset_command) refers to uart_bsp.o(.text.process_reset_command) for [Anonymous Symbol]
    uart_bsp.o(.text.save_initial_position) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.save_initial_position) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.save_initial_position) refers to usart.o(.bss.huart2) for huart2
    uart_bsp.o(.text.save_initial_position) refers to emm_v5.o(.text.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    uart_bsp.o(.text.save_initial_position) refers to usart.o(.bss.huart4) for huart4
    uart_bsp.o(.text.save_initial_position) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.save_initial_position) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.ARM.exidx.text.save_initial_position) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.save_initial_position) refers to uart_bsp.o(.text.save_initial_position) for [Anonymous Symbol]
    uart_bsp.o(.text.process_command) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.process_command) refers to strncmp.o(.text) for strncmp
    uart_bsp.o(.text.process_command) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.process_command) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.process_command) refers to uart_bsp.o(.rodata.str1.1) for .L.str.35
    uart_bsp.o(.text.process_command) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.process_command) refers to usart.o(.bss.huart2) for huart2
    uart_bsp.o(.text.process_command) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    uart_bsp.o(.text.process_command) refers to usart.o(.bss.huart4) for huart4
    uart_bsp.o(.text.process_command) refers to __0sscanf.o(.text) for __0sscanf
    uart_bsp.o(.ARM.exidx.text.process_command) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.process_command) refers to uart_bsp.o(.text.process_command) for [Anonymous Symbol]
    uart_bsp.o(.text.uart_proc) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_bsp.o(.text.uart_proc) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.bss.output_buffer_x) for output_buffer_x
    uart_bsp.o(.text.uart_proc) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_bsp.o(.text.uart_proc) refers to emm_v5.o(.text.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.text.parse_x_motor_data) for parse_x_motor_data
    uart_bsp.o(.text.uart_proc) refers to usart.o(.bss.huart1) for huart1
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.text.my_printf) for my_printf
    uart_bsp.o(.text.uart_proc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.bss.output_buffer_y) for output_buffer_y
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.text.parse_y_motor_data) for parse_y_motor_data
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.bss.output_buffer_pi) for output_buffer_pi
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.bss.line_buffer) for line_buffer
    uart_bsp.o(.text.uart_proc) refers to uart_bsp.o(.rodata.str1.1) for .L.str.46
    uart_bsp.o(.text.uart_proc) refers to pi_bsp.o(.text.pi_parse_data) for pi_parse_data
    uart_bsp.o(.ARM.exidx.text.uart_proc) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.ARM.exidx.text.uart_proc) refers to uart_bsp.o(.text.uart_proc) for [Anonymous Symbol]
    uart_bsp.o(.rodata.str1.1) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.output_buffer_x) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.output_buffer_y) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.output_buffer_pi) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.line_buffer) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.ringbuffer_pool_x) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.ringbuffer_pool_y) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss.ringbuffer_pool_pi) refers (Special) to _scanf_int.o(.text) for _scanf_int
    uart_bsp.o(.bss..L_MergedGlobals) refers (Special) to _scanf_int.o(.text) for _scanf_int
    step_motor_bsp.o(.text.Step_Motor_Init) refers to usart.o(.bss.huart2) for huart2
    step_motor_bsp.o(.text.Step_Motor_Init) refers to emm_v5.o(.text.Emm_V5_En_Control) for Emm_V5_En_Control
    step_motor_bsp.o(.text.Step_Motor_Init) refers to usart.o(.bss.huart4) for huart4
    step_motor_bsp.o(.text.Step_Motor_Init) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Init) refers to step_motor_bsp.o(.text.Step_Motor_Init) for [Anonymous Symbol]
    step_motor_bsp.o(.text.Step_Motor_Stop) refers to usart.o(.bss.huart2) for huart2
    step_motor_bsp.o(.text.Step_Motor_Stop) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    step_motor_bsp.o(.text.Step_Motor_Stop) refers to usart.o(.bss.huart4) for huart4
    step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Stop) refers to step_motor_bsp.o(.text.Step_Motor_Stop) for [Anonymous Symbol]
    step_motor_bsp.o(.text.Step_Motor_Set_Speed) refers to usart.o(.bss.huart1) for huart1
    step_motor_bsp.o(.text.Step_Motor_Set_Speed) refers to uart_bsp.o(.text.my_printf) for my_printf
    step_motor_bsp.o(.text.Step_Motor_Set_Speed) refers to usart.o(.bss.huart2) for huart2
    step_motor_bsp.o(.text.Step_Motor_Set_Speed) refers to emm_v5.o(.text.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(.text.Step_Motor_Set_Speed) refers to usart.o(.bss.huart4) for huart4
    step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Speed) refers to step_motor_bsp.o(.text.Step_Motor_Set_Speed) for [Anonymous Symbol]
    step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) refers to usart.o(.bss.huart2) for huart2
    step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) refers to emm_v5.o(.text.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) refers to usart.o(.bss.huart4) for huart4
    step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Speed_my) refers to step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) for [Anonymous Symbol]
    step_motor_bsp.o(.text.Step_Motor_Set_Pwm) refers to usart.o(.bss.huart2) for huart2
    step_motor_bsp.o(.text.Step_Motor_Set_Pwm) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    step_motor_bsp.o(.text.Step_Motor_Set_Pwm) refers to usart.o(.bss.huart4) for huart4
    step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Pwm) refers to step_motor_bsp.o(.text.Step_Motor_Set_Pwm) for [Anonymous Symbol]
    step_motor_bsp.o(.ARM.exidx.text.step_motor_proc) refers to step_motor_bsp.o(.text.step_motor_proc) for [Anonymous Symbol]
    pi_bsp.o(.text) refers (Special) to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(.text.pi_parse_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(.text.pi_parse_data) refers to strncmp.o(.text) for strncmp
    pi_bsp.o(.text.pi_parse_data) refers to __0sscanf.o(.text) for __0sscanf
    pi_bsp.o(.text.pi_parse_data) refers to pi_bsp.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pi_bsp.o(.text.pi_parse_data) refers to usart.o(.bss.huart1) for huart1
    pi_bsp.o(.text.pi_parse_data) refers to uart_bsp.o(.text.my_printf) for my_printf
    pi_bsp.o(.ARM.exidx.text.pi_parse_data) refers (Special) to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(.ARM.exidx.text.pi_parse_data) refers to pi_bsp.o(.text.pi_parse_data) for [Anonymous Symbol]
    pi_bsp.o(.text.pi_proc) refers (Special) to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(.text.pi_proc) refers to pi_bsp.o(.data..L_MergedGlobals) for .L_MergedGlobals
    pi_bsp.o(.text.pi_proc) refers to mypid.o(.bss.pid_x) for pid_x
    pi_bsp.o(.text.pi_proc) refers to mypid.o(.text.pid_calc) for pid_calc
    pi_bsp.o(.text.pi_proc) refers to mypid.o(.bss.pid_y) for pid_y
    pi_bsp.o(.text.pi_proc) refers to step_motor_bsp.o(.text.Step_Motor_Set_Speed_my) for Step_Motor_Set_Speed_my
    pi_bsp.o(.ARM.exidx.text.pi_proc) refers (Special) to _scanf_int.o(.text) for _scanf_int
    pi_bsp.o(.ARM.exidx.text.pi_proc) refers to pi_bsp.o(.text.pi_proc) for [Anonymous Symbol]
    pi_bsp.o(.data..L_MergedGlobals) refers (Special) to _scanf_int.o(.text) for _scanf_int
    emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero) refers to emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro) refers to emm_v5.o(.text.Emm_V5_Reset_Clog_Pro) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params) refers to emm_v5.o(.text.Emm_V5_Read_Sys_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode) refers to emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control) refers to emm_v5.o(.text.Emm_V5_En_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to usart.o(.bss.huart1) for huart1
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to uart_bsp.o(.text.my_printf) for my_printf
    emm_v5.o(.text.Emm_V5_Vel_Control) refers to emm_v5.o(.rodata.str1.1) for .L.str.2
    emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control) refers to emm_v5.o(.text.Emm_V5_Vel_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control) refers to emm_v5.o(.text.Emm_V5_Pos_Control) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now) refers to emm_v5.o(.text.Emm_V5_Stop_Now) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion) refers to emm_v5.o(.text.Emm_V5_Synchronous_motion) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O) refers to emm_v5.o(.text.Emm_V5_Origin_Set_O) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params) refers to emm_v5.o(.text.Emm_V5_Origin_Modify_Params) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return) refers to emm_v5.o(.text.Emm_V5_Origin_Trigger_Return) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt) refers to emm_v5.o(.text.Emm_V5_Origin_Interrupt) for [Anonymous Symbol]
    emm_v5.o(.text.Emm_V5_Parse_Response) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(.ARM.exidx.text.Emm_V5_Parse_Response) refers to emm_v5.o(.text.Emm_V5_Parse_Response) for [Anonymous Symbol]
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_x) for pid_x
    mypid.o(.text.PID_INIT) refers to mypid.o(.text.pid_param_init) for pid_param_init
    mypid.o(.text.PID_INIT) refers to mypid.o(.text.pid_reset) for pid_reset
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_y) for pid_y
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_speed_left) for pid_speed_left
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_speed_right) for pid_speed_right
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_location_left) for pid_location_left
    mypid.o(.text.PID_INIT) refers to mypid.o(.bss.pid_location_right) for pid_location_right
    mypid.o(.ARM.exidx.text.PID_INIT) refers to mypid.o(.text.PID_INIT) for [Anonymous Symbol]
    mypid.o(.text.PID_struct_init) refers to mypid.o(.text.pid_param_init) for pid_param_init
    mypid.o(.text.PID_struct_init) refers to mypid.o(.text.pid_reset) for pid_reset
    mypid.o(.ARM.exidx.text.PID_struct_init) refers to mypid.o(.text.PID_struct_init) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.abs_limit) refers to mypid.o(.text.abs_limit) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_calc) refers to mypid.o(.text.pid_calc) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_calc_i_separation) refers to mypid.o(.text.pid_calc_i_separation) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_calc_d) refers to mypid.o(.text.pid_calc_d) for [Anonymous Symbol]
    mypid.o(.text.pid_angle_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    mypid.o(.text.pid_angle_calc) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    mypid.o(.text.pid_angle_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mypid.o(.ARM.exidx.text.pid_angle_calc) refers to mypid.o(.text.pid_angle_calc) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_yaw_calc) refers to mypid.o(.text.pid_yaw_calc) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_clear) refers to mypid.o(.text.pid_clear) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_param_init) refers to mypid.o(.text.pid_param_init) for [Anonymous Symbol]
    mypid.o(.ARM.exidx.text.pid_reset) refers to mypid.o(.text.pid_reset) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fmod.o(i.__hardfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod.o(i.__hardfp_fmod) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to _rserrno.o(.text) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7em.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C1_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C2_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (92 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (92 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM4_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Encoder_MspDeInit), (96 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART4_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART5_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART3_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART6_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (366 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.bss.user_rx_buf), (64 bytes).
    Removing usart.o(.bss.uart3_rx_buffer), (32 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (376 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (272 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (518 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (580 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (290 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (326 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (462 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_Delay), (40 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (256 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (294 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (354 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (594 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (130 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (552 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (436 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (454 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (578 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (644 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (628 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (212 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (222 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (282 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (154 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (384 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (540 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_Write_cmd), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write_cmd), (8 bytes).
    Removing oled.o(.text.OLED_Write_data), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Write_data), (8 bytes).
    Removing oled.o(.text.OLED_ShowPic), (340 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowPic), (8 bytes).
    Removing oled.o(.text.OLED_Set_Position), (114 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Set_Position), (8 bytes).
    Removing oled.o(.text.OLED_ShowHanzi), (1112 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHanzi), (8 bytes).
    Removing oled.o(.text.OLED_ShowHzbig), (578 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHzbig), (8 bytes).
    Removing oled.o(.text.OLED_ShowFloat), (364 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowFloat), (8 bytes).
    Removing oled.o(.text.OLED_ShowChar), (932 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.text.OLED_ShowNum), (280 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowStr), (64 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowStr), (8 bytes).
    Removing oled.o(.text.OLED_Allfill), (536 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Allfill), (8 bytes).
    Removing oled.o(.text.OLED_Clear), (540 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.text.OLED_Display_On), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_On), (8 bytes).
    Removing oled.o(.text.OLED_Display_Off), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Display_Off), (8 bytes).
    Removing oled.o(.text.OLED_Init), (706 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.rodata.F6X8), (552 bytes).
    Removing oled.o(.rodata.F8X16), (1520 bytes).
    Removing oled.o(.rodata.Hzk), (128 bytes).
    Removing oled.o(.rodata.Hzb), (512 bytes).
    Removing oled.o(.data.initcmd1), (22 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_put_force), (234 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_peek), (158 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar), (124 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar_force), (128 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_getchar), (140 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_reset), (34 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset), (8 bytes).
    Removing schedule.o(.text), (0 bytes).
    Removing schedule.o(.ARM.exidx.text.schedule_init), (8 bytes).
    Removing schedule.o(.ARM.exidx.text.schedule_run), (8 bytes).
    Removing oled_bsp.o(.text), (0 bytes).
    Removing oled_bsp.o(.text.Oled_Printf), (54 bytes).
    Removing oled_bsp.o(.ARM.exidx.text.Oled_Printf), (8 bytes).
    Removing oled_bsp.o(.text.oled_proc), (2 bytes).
    Removing oled_bsp.o(.ARM.exidx.text.oled_proc), (8 bytes).
    Removing key_bsp.o(.text), (0 bytes).
    Removing key_bsp.o(.text.key_read), (76 bytes).
    Removing key_bsp.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing key_bsp.o(.text.key_proc), (98 bytes).
    Removing key_bsp.o(.ARM.exidx.text.key_proc), (8 bytes).
    Removing key_bsp.o(.bss..L_MergedGlobals), (4 bytes).
    Removing uart_bsp.o(.text), (0 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_bsp.o(.text.check_motor_angle_limits), (304 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.check_motor_angle_limits), (8 bytes).
    Removing uart_bsp.o(.text.calc_motor_angle), (48 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.calc_motor_angle), (8 bytes).
    Removing uart_bsp.o(.text.calc_relative_angle), (68 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.calc_relative_angle), (8 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.parse_x_motor_data), (8 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.parse_y_motor_data), (8 bytes).
    Removing uart_bsp.o(.text.process_reset_command), (126 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.process_reset_command), (8 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.save_initial_position), (8 bytes).
    Removing uart_bsp.o(.text.process_command), (264 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.process_command), (8 bytes).
    Removing uart_bsp.o(.ARM.exidx.text.uart_proc), (8 bytes).
    Removing step_motor_bsp.o(.text), (0 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Init), (8 bytes).
    Removing step_motor_bsp.o(.text.Step_Motor_Stop), (38 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Stop), (8 bytes).
    Removing step_motor_bsp.o(.text.Step_Motor_Set_Speed), (228 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Speed), (8 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Speed_my), (8 bytes).
    Removing step_motor_bsp.o(.text.Step_Motor_Set_Pwm), (80 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.Step_Motor_Set_Pwm), (8 bytes).
    Removing step_motor_bsp.o(.text.step_motor_proc), (2 bytes).
    Removing step_motor_bsp.o(.ARM.exidx.text.step_motor_proc), (8 bytes).
    Removing pi_bsp.o(.text), (0 bytes).
    Removing pi_bsp.o(.ARM.exidx.text.pi_parse_data), (8 bytes).
    Removing pi_bsp.o(.text.pi_proc), (98 bytes).
    Removing pi_bsp.o(.ARM.exidx.text.pi_proc), (8 bytes).
    Removing emm_v5.o(.text), (0 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_CurPos_To_Zero), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Reset_Clog_Pro), (46 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Reset_Clog_Pro), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Read_Sys_Params), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Modify_Ctrl_Mode), (62 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Modify_Ctrl_Mode), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_En_Control), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Vel_Control), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Pos_Control), (102 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Pos_Control), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Stop_Now), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Synchronous_motion), (46 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Synchronous_motion), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Set_O), (54 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Set_O), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Modify_Params), (148 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Modify_Params), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Trigger_Return), (60 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Trigger_Return), (8 bytes).
    Removing emm_v5.o(.text.Emm_V5_Origin_Interrupt), (46 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Origin_Interrupt), (8 bytes).
    Removing emm_v5.o(.ARM.exidx.text.Emm_V5_Parse_Response), (8 bytes).
    Removing mypid.o(.text), (0 bytes).
    Removing mypid.o(.ARM.exidx.text.PID_INIT), (8 bytes).
    Removing mypid.o(.text.PID_struct_init), (42 bytes).
    Removing mypid.o(.ARM.exidx.text.PID_struct_init), (8 bytes).
    Removing mypid.o(.text.abs_limit), (56 bytes).
    Removing mypid.o(.ARM.exidx.text.abs_limit), (8 bytes).
    Removing mypid.o(.text.pid_calc), (484 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_calc), (8 bytes).
    Removing mypid.o(.text.pid_calc_i_separation), (496 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_calc_i_separation), (8 bytes).
    Removing mypid.o(.text.pid_calc_d), (328 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_calc_d), (8 bytes).
    Removing mypid.o(.text.pid_angle_calc), (472 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_angle_calc), (8 bytes).
    Removing mypid.o(.text.pid_yaw_calc), (404 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_yaw_calc), (8 bytes).
    Removing mypid.o(.text.pid_clear), (12 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_clear), (8 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_param_init), (8 bytes).
    Removing mypid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing mypid.o(.data.maxout), (4 bytes).
    Removing mypid.o(.data.i_out_limit), (4 bytes).
    Removing mypid.o(.bss.Kp), (4 bytes).
    Removing mypid.o(.bss.Ki), (4 bytes).
    Removing mypid.o(.bss.Kd), (4 bytes).
    Removing mypid.o(.bss.target), (4 bytes).
    Removing mypid.o(.bss.actual), (4 bytes).
    Removing mypid.o(.bss.pid_out), (4 bytes).

1019 unused section(s) (total 71080 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7em.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/drem.s                          0x00000000   Number         0  drem_clz.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    Emm_V5.c                                 0x00000000   Number         0  emm_v5.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    key_bsp.c                                0x00000000   Number         0  key_bsp.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    mypid.c                                  0x00000000   Number         0  mypid.o ABSOLUTE
    oled.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    oled_bsp.c                               0x00000000   Number         0  oled_bsp.o ABSOLUTE
    pi_bsp.c                                 0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    schedule.c                               0x00000000   Number         0  schedule.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    step_motor_bsp.c                         0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    uart_bsp.c                               0x00000000   Number         0  uart_bsp.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001ec   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x08000208   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x0800020c   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000228   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000228   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x0800022e   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000234   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800023a   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000240   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x08000246   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x0800024c   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x08000256   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x0800025c   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000262   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x08000268   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x0800026e   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000274   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800027a   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000280   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x08000286   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x0800028c   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000292   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x0800029c   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002a2   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002a8   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002ae   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002b4   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002b8   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ba   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x080002be   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x080002be   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080002c4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x080002c4   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x080002d0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080002d0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x080002d0   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x080002da   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x080002dc   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002de   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002de   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002e0   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002e0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002e0   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002e6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002e6   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002ea   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002ea   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002f2   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002f4   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002f4   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002f8   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    $v0                                      0x08000300   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000300   Section       64  startup_stm32f407xx.o(.text)
    .text                                    0x08000340   Section      240  lludivv7m.o(.text)
    .text                                    0x08000430   Section        0  vsnprintf.o(.text)
    .text                                    0x0800046c   Section        0  __0sscanf.o(.text)
    .text                                    0x080004a8   Section        0  _scanf_int.o(.text)
    .text                                    0x080005f4   Section        0  assert.o(.text)
    .text                                    0x08000674   Section        0  strncmp.o(.text)
    .text                                    0x0800070a   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x08000794   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080007e2   Section        0  heapauxi.o(.text)
    .text                                    0x080007e8   Section        0  _printf_pad.o(.text)
    .text                                    0x08000836   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800085a   Section        0  _printf_str.o(.text)
    .text                                    0x080008ac   Section        0  _printf_dec.o(.text)
    .text                                    0x08000924   Section        0  _printf_charcount.o(.text)
    _printf_input_char                       0x0800094d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800094c   Section        0  _printf_char_common.o(.text)
    .text                                    0x0800097c   Section        0  _snputc.o(.text)
    .text                                    0x0800098c   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000a48   Section        0  _printf_longlong_dec.o(.text)
    _printf_longlong_oct_internal            0x08000ac5   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000ac4   Section        0  _printf_oct_int_ll.o(.text)
    _printf_hex_common                       0x08000b35   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000b34   Section        0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000bc8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000d50   Section        0  _chval.o(.text)
    _scanf_char_input                        0x08000d6d   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000d6c   Section        0  scanf_char.o(.text)
    .text                                    0x08000d98   Section        0  _sgetc.o(.text)
    .text                                    0x08000dd8   Section        0  abort.o(.text)
    .text                                    0x08000dee   Section        0  assert_puts.o(.text)
    .text                                    0x08000e02   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000e66   Section        0  sys_wrch.o(.text)
    .text                                    0x08000e74   Section        0  sys_exit.o(.text)
    .text                                    0x08000e80   Section      138  lludiv10.o(.text)
    .text                                    0x08000f0a   Section        0  isspace.o(.text)
    .text                                    0x08000f1c   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x08000fcf   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000fce   Section        0  _printf_fp_dec.o(.text)
    .text                                    0x080013ec   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080016e8   Section        0  _printf_char.o(.text)
    .text                                    0x08001714   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001740   Section        0  _scanf.o(.text)
    .text                                    0x08001ab4   Section        0  _c16rtomb.o(.text)
    .text                                    0x08001afc   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08001b0c   Section        8  libspace.o(.text)
    .text                                    0x08001b14   Section        2  use_no_semi.o(.text)
    .text                                    0x08001b16   Section        0  indicate_semi.o(.text)
    .text                                    0x08001b16   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001b60   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001b70   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001b78   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001bf8   Section        0  bigflt0.o(.text)
    .text                                    0x08001cdc   Section        0  exit.o(.text)
    .text                                    0x08001cee   Section        0  defsig_exit.o(.text)
    .text                                    0x08001cf8   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001d28   Section        0  defsig_general.o(.text)
    .text                                    0x08001d60   Section      124  strcmpv7em.o(.text)
    [Anonymous Symbol]                       0x08001ddc   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08001de0   Section        0  stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08001dec   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x08001df8   Section        0  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    [Anonymous Symbol]                       0x08001e04   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08001e08   Section        0  emm_v5.o(.text.Emm_V5_En_Control)
    [Anonymous Symbol]                       0x08001e48   Section        0  emm_v5.o(.text.Emm_V5_Parse_Response)
    [Anonymous Symbol]                       0x08002174   Section        0  emm_v5.o(.text.Emm_V5_Read_Sys_Params)
    [Anonymous Symbol]                       0x0800224c   Section        0  emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero)
    [Anonymous Symbol]                       0x0800227c   Section        0  emm_v5.o(.text.Emm_V5_Stop_Now)
    [Anonymous Symbol]                       0x080022b4   Section        0  emm_v5.o(.text.Emm_V5_Vel_Control)
    [Anonymous Symbol]                       0x080023d4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080023dc   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x0800246c   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x08002490   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x08002654   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x080027b8   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x0800285c   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080029fc   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08002a08   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08002a14   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x08002b78   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x08002c48   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08002c64   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08002c9c   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08002ce4   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08002d1c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08002d40   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08002d98   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08002db8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08002f1c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08002f44   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08002f6c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08002fd8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08003384   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x080033b0   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x080033fc   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x080034b8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x08003514   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08003548   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x080036e8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    [Anonymous Symbol]                       0x0800379c   Section        0  tim.o(.text.HAL_TIM_Encoder_MspInit)
    [Anonymous Symbol]                       0x0800385c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x08003a7c   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x08003ad8   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x08003adc   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x08003ccc   Section        0  usart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08003d70   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08003d74   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08004308   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08004368   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08004714   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08004718   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x0800471c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x080048b0   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x080048b4   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080048b8   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x08004924   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08004a64   Section        0  i2c.o(.text.MX_I2C1_Init)
    [Anonymous Symbol]                       0x08004aa8   Section        0  i2c.o(.text.MX_I2C2_Init)
    [Anonymous Symbol]                       0x08004aec   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08004c54   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08004cbc   Section        0  tim.o(.text.MX_TIM4_Init)
    [Anonymous Symbol]                       0x08004d24   Section        0  usart.o(.text.MX_UART4_Init)
    [Anonymous Symbol]                       0x08004d84   Section        0  usart.o(.text.MX_UART5_Init)
    [Anonymous Symbol]                       0x08004dc0   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08004dfc   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08004e5c   Section        0  usart.o(.text.MX_USART3_UART_Init)
    [Anonymous Symbol]                       0x08004e98   Section        0  usart.o(.text.MX_USART6_UART_Init)
    [Anonymous Symbol]                       0x08004ef8   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08004efc   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08004f00   Section        0  mypid.o(.text.PID_INIT)
    [Anonymous Symbol]                       0x0800500c   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08005010   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08005014   Section        0  step_motor_bsp.o(.text.Step_Motor_Init)
    [Anonymous Symbol]                       0x08005058   Section        0  step_motor_bsp.o(.text.Step_Motor_Set_Speed_my)
    [Anonymous Symbol]                       0x0800512c   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08005130   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x080051d4   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x080051e8   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x08005324   Section        0  stm32f4xx_it.o(.text.UART4_IRQHandler)
    UART_DMAAbortOnError                     0x08005359   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08005358   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x08005365   Thumb Code   380  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08005364   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x080054e1   Thumb Code   350  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x080054e0   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x08005641   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x08005640   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x08005659   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08005658   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08005759   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08005758   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08005840   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x0800584c   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x08005880   Section        0  stm32f4xx_it.o(.text.USART3_IRQHandler)
    [Anonymous Symbol]                       0x0800588c   Section        0  stm32f4xx_it.o(.text.USART6_IRQHandler)
    [Anonymous Symbol]                       0x080058c0   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x080058c4   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08005988   Section        0  uart_bsp.o(.text.my_printf)
    [Anonymous Symbol]                       0x080059c8   Section        0  uart_bsp.o(.text.parse_x_motor_data)
    [Anonymous Symbol]                       0x08005d74   Section        0  uart_bsp.o(.text.parse_y_motor_data)
    [Anonymous Symbol]                       0x08006120   Section        0  pi_bsp.o(.text.pi_parse_data)
    pid_param_init                           0x08006229   Thumb Code    14  mypid.o(.text.pid_param_init)
    [Anonymous Symbol]                       0x08006228   Section        0  mypid.o(.text.pid_param_init)
    pid_reset                                0x08006239   Thumb Code    16  mypid.o(.text.pid_reset)
    [Anonymous Symbol]                       0x08006238   Section        0  mypid.o(.text.pid_reset)
    [Anonymous Symbol]                       0x08006248   Section        0  ringbuffer.o(.text.rt_ringbuffer_data_len)
    [Anonymous Symbol]                       0x0800628c   Section        0  ringbuffer.o(.text.rt_ringbuffer_get)
    [Anonymous Symbol]                       0x08006360   Section        0  ringbuffer.o(.text.rt_ringbuffer_init)
    [Anonymous Symbol]                       0x080063b0   Section        0  ringbuffer.o(.text.rt_ringbuffer_put)
    [Anonymous Symbol]                       0x08006480   Section        0  uart_bsp.o(.text.save_initial_position)
    [Anonymous Symbol]                       0x080064e4   Section        0  schedule.o(.text.schedule_init)
    [Anonymous Symbol]                       0x080064f4   Section        0  schedule.o(.text.schedule_run)
    [Anonymous Symbol]                       0x08006540   Section        0  uart_bsp.o(.text.uart_proc)
    CL$$btod_d2e                             0x08006738   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08006776   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080067bc   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800681c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08006b54   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08006c30   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08006c5a   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08006c84   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08006ec8   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08006ef8   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08006f08   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08006f34   Section       44  lc_ctype_c.o(locale$$code)
    $v0                                      0x08006f60   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$dretinf                            0x08006f60   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08006f6c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$f2d                                0x08006f6c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08006fc2   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fnaninf                            0x08006fc2   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800704e   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fpinit                             0x0800704e   Section       26  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08007068   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf1                            0x08007068   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800706c   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$printf2                            0x0800706c   Section        4  printf2.o(x$fpl$printf2)
    initial_mbstate                          0x08007070   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08007070   Section        8  _printf_wctomb.o(.constdata)
    x$fpl$usenofp                            0x08007070   Section        0  usenofp.o(x$fpl$usenofp)
    uc_hextab                                0x08007078   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08007078   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800708c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    maptable                                 0x080070a0   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080070a0   Section       17  __printf_flags_ss_wp.o(.constdata)
    lc_hextab                                0x080070b1   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080070b1   Section       38  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080070c4   Data          19  _printf_fp_hex.o(.constdata)
    tenpwrs_x                                0x080070d8   Data          60  bigflt0.o(.constdata)
    .constdata                               0x080070d8   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x08007114   Data          64  bigflt0.o(.constdata)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x08007184   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x08007184   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    .L.str.3                                 0x0800718c   Data          14  ringbuffer.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800718c   Section        0  ringbuffer.o(.rodata.str1.1)
    .L.str.1                                 0x0800719a   Data          27  ringbuffer.o(.rodata.str1.1)
    .L.str.46                                0x080071b5   Data          33  uart_bsp.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080071b5   Section        0  uart_bsp.o(.rodata.str1.1)
    .L.str.47                                0x080071d6   Data          49  uart_bsp.o(.rodata.str1.1)
    .L.str.8                                 0x0800722e   Data          38  uart_bsp.o(.rodata.str1.1)
    .L.str.24                                0x08007254   Data          38  uart_bsp.o(.rodata.str1.1)
    .L.str.2                                 0x0800727a   Data          42  uart_bsp.o(.rodata.str1.1)
    .L.str.19                                0x080072a4   Data          42  uart_bsp.o(.rodata.str1.1)
    .L.str.7                                 0x080072ce   Data          40  uart_bsp.o(.rodata.str1.1)
    .L.str.23                                0x080072f6   Data          40  uart_bsp.o(.rodata.str1.1)
    .L.str.9                                 0x0800731e   Data          39  uart_bsp.o(.rodata.str1.1)
    .L.str.25                                0x08007345   Data          39  uart_bsp.o(.rodata.str1.1)
    .L.str.4                                 0x0800736c   Data          37  uart_bsp.o(.rodata.str1.1)
    .L.str.6                                 0x08007391   Data          38  uart_bsp.o(.rodata.str1.1)
    .L.str.22                                0x080073b7   Data          38  uart_bsp.o(.rodata.str1.1)
    .L.str.14                                0x080073dd   Data          30  uart_bsp.o(.rodata.str1.1)
    .L.str.30                                0x080073fb   Data          30  uart_bsp.o(.rodata.str1.1)
    .L.str.18                                0x08007419   Data          58  uart_bsp.o(.rodata.str1.1)
    .L.str.34                                0x08007453   Data          58  uart_bsp.o(.rodata.str1.1)
    .L.str.11                                0x080074bd   Data          24  uart_bsp.o(.rodata.str1.1)
    .L.str.27                                0x080074d5   Data          24  uart_bsp.o(.rodata.str1.1)
    .L.str.12                                0x080074ed   Data          27  uart_bsp.o(.rodata.str1.1)
    .L.str.28                                0x08007508   Data          27  uart_bsp.o(.rodata.str1.1)
    .L.str.5                                 0x08007523   Data          86  uart_bsp.o(.rodata.str1.1)
    .L.str.21                                0x08007579   Data          86  uart_bsp.o(.rodata.str1.1)
    .L.str.35                                0x08007588   Data          39  uart_bsp.o(.rodata.str1.1)
    .L.str.36                                0x080075af   Data          48  uart_bsp.o(.rodata.str1.1)
    .L.str.3                                 0x080075cf   Data          49  uart_bsp.o(.rodata.str1.1)
    .L.str.20                                0x08007600   Data          49  uart_bsp.o(.rodata.str1.1)
    .L.str.2                                 0x08007631   Data           8  emm_v5.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08007631   Section        0  emm_v5.o(.rodata.str1.1)
    locale$$data                             0x0800765c   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08007660   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007668   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08007674   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08007676   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08007677   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08007678   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08007678   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x0800767c   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08007684   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08007788   Data           0  lc_ctype_c.o(locale$$data)
    .L_MergedGlobals                         0x20000000   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000008   Data          32  pi_bsp.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  pi_bsp.o(.data..L_MergedGlobals)
    schedule_task                            0x2000002c   Data          12  schedule.o(.data.schedule_task)
    [Anonymous Symbol]                       0x2000002c   Section        0  schedule.o(.data.schedule_task)
    .bss                                     0x20000038   Section       96  libspace.o(.bss)
    .L_MergedGlobals                         0x20000098   Data          80  uart_bsp.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000098   Section        0  uart_bsp.o(.bss..L_MergedGlobals)
    line_buffer_idx                          0x200000c0   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    line_buffer                              0x20000538   Data         128  uart_bsp.o(.bss.line_buffer)
    [Anonymous Symbol]                       0x20000538   Section        0  uart_bsp.o(.bss.line_buffer)
    Heap_Mem                                 0x200009e0   Data         512  startup_stm32f407xx.o(HEAP)
    HEAP                                     0x200009e0   Section      512  startup_stm32f407xx.o(HEAP)
    Stack_Mem                                0x20000be0   Data        1024  startup_stm32f407xx.o(STACK)
    STACK                                    0x20000be0   Section     1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000fe0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x0800019b   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001ed   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x08000209   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x0800020d   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000229   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000229   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x0800022f   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000235   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800023b   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000241   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x08000247   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x0800024d   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x08000257   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x0800025d   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000263   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x08000269   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x0800026f   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000275   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800027b   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000281   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x08000287   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x0800028d   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000293   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x0800029d   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002a3   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002a9   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002af   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002b5   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002b9   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x080002bf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x080002c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x080002c5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x080002d1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x080002dd   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002df   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002e1   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002e1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002e1   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002e7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002e7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002eb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002eb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002f3   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002f5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002f5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002f9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000301   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800031b   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x0800031d   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000341   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000341   Thumb Code   240  lludivv7m.o(.text)
    vsnprintf                                0x08000431   Thumb Code    56  vsnprintf.o(.text)
    __0sscanf                                0x0800046d   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x080004a9   Thumb Code   332  _scanf_int.o(.text)
    __aeabi_assert                           0x080005f5   Thumb Code    86  assert.o(.text)
    __assert                                 0x080005f5   Thumb Code     0  assert.o(.text)
    strncmp                                  0x08000675   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x0800070b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800070b   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000771   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x08000795   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000795   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000795   Thumb Code     0  rt_memclr_w.o(.text)
    _memset_w                                0x08000799   Thumb Code    74  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080007e3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080007e5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080007e7   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x080007e9   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000815   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000837   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000849   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800085b   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080008ad   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000925   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000957   Thumb Code    32  _printf_char_common.o(.text)
    _snputc                                  0x0800097d   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x0800098d   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000a49   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000ac5   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000b09   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000b21   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000b35   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000b8b   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000ba7   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000bb3   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000bc9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _chval                                   0x08000d51   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000d79   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000d99   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000db7   Thumb Code    34  _sgetc.o(.text)
    abort                                    0x08000dd9   Thumb Code    22  abort.o(.text)
    __assert_puts                            0x08000def   Thumb Code    20  assert_puts.o(.text)
    __aeabi_memcpy4                          0x08000e03   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000e03   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000e03   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000e4b   Thumb Code     0  rt_memcpy_w.o(.text)
    _ttywrch                                 0x08000e67   Thumb Code    14  sys_wrch.o(.text)
    _sys_exit                                0x08000e75   Thumb Code     8  sys_exit.o(.text)
    _ll_udiv10                               0x08000e81   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000f0b   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08000f1d   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x0800117f   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x080013ed   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080016e9   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080016fd   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800170d   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001715   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001729   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001739   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x08001741   Thumb Code   878  _scanf.o(.text)
    _c16rtomb                                0x08001ab5   Thumb Code    72  _c16rtomb.o(.text)
    _wcrtomb                                 0x08001ab5   Thumb Code     0  _c16rtomb.o(.text)
    __rt_SIGABRT                             0x08001afd   Thumb Code    14  defsig_abrt_outer.o(.text)
    __user_libspace                          0x08001b0d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001b0d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001b0d   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08001b15   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001b15   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001b17   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08001b17   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001b61   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001b71   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001b79   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001bf9   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001cdd   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x08001cef   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001cf9   Thumb Code    14  defsig_abrt_inner.o(.text)
    __default_signal_display                 0x08001d29   Thumb Code    50  defsig_general.o(.text)
    strcmp                                   0x08001d61   Thumb Code   124  strcmpv7em.o(.text)
    BusFault_Handler                         0x08001ddd   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA1_Stream2_IRQHandler                  0x08001de1   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x08001ded   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08001df9   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    DebugMon_Handler                         0x08001e05   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Emm_V5_En_Control                        0x08001e09   Thumb Code    62  emm_v5.o(.text.Emm_V5_En_Control)
    Emm_V5_Parse_Response                    0x08001e49   Thumb Code   810  emm_v5.o(.text.Emm_V5_Parse_Response)
    Emm_V5_Read_Sys_Params                   0x08002175   Thumb Code   216  emm_v5.o(.text.Emm_V5_Read_Sys_Params)
    Emm_V5_Reset_CurPos_To_Zero              0x0800224d   Thumb Code    46  emm_v5.o(.text.Emm_V5_Reset_CurPos_To_Zero)
    Emm_V5_Stop_Now                          0x0800227d   Thumb Code    54  emm_v5.o(.text.Emm_V5_Stop_Now)
    Emm_V5_Vel_Control                       0x080022b5   Thumb Code   224  emm_v5.o(.text.Emm_V5_Vel_Control)
    Error_Handler                            0x080023d5   Thumb Code     6  main.o(.text.Error_Handler)
    HAL_DMA_Abort                            0x080023dd   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x0800246d   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002491   Thumb Code   452  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002655   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080027b9   Thumb Code   162  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x0800285d   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080029fd   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002a09   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_Init                             0x08002a15   Thumb Code   356  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08002b79   Thumb Code   206  i2c.o(.text.HAL_I2C_MspInit)
    HAL_IncTick                              0x08002c49   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08002c65   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08002c9d   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08002ce5   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002d1d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002d41   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002d99   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002db9   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002f1d   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002f45   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002f6d   Thumb Code   106  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002fd9   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003385   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x080033b1   Thumb Code    76  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x080033fd   Thumb Code   186  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080034b9   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003515   Thumb Code    50  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08003549   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080036e9   Thumb Code   178  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x0800379d   Thumb Code   192  tim.o(.text.HAL_TIM_Encoder_MspInit)
    HAL_TIM_PWM_ConfigChannel                0x0800385d   Thumb Code   544  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003a7d   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003ad9   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003add   Thumb Code   494  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003ccd   Thumb Code   164  usart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003d71   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003d75   Thumb Code  1428  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004309   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004369   Thumb Code   940  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08004715   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004719   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800471d   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080048b1   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080048b5   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_DMA_Init                              0x080048b9   Thumb Code   108  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x08004925   Thumb Code   320  gpio.o(.text.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004a65   Thumb Code    66  i2c.o(.text.MX_I2C1_Init)
    MX_I2C2_Init                             0x08004aa9   Thumb Code    66  i2c.o(.text.MX_I2C2_Init)
    MX_TIM1_Init                             0x08004aed   Thumb Code   358  tim.o(.text.MX_TIM1_Init)
    MX_TIM3_Init                             0x08004c55   Thumb Code   104  tim.o(.text.MX_TIM3_Init)
    MX_TIM4_Init                             0x08004cbd   Thumb Code   104  tim.o(.text.MX_TIM4_Init)
    MX_UART4_Init                            0x08004d25   Thumb Code    96  usart.o(.text.MX_UART4_Init)
    MX_UART5_Init                            0x08004d85   Thumb Code    60  usart.o(.text.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08004dc1   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004dfd   Thumb Code    96  usart.o(.text.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004e5d   Thumb Code    60  usart.o(.text.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08004e99   Thumb Code    96  usart.o(.text.MX_USART6_UART_Init)
    MemManage_Handler                        0x08004ef9   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08004efd   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    PID_INIT                                 0x08004f01   Thumb Code   266  mypid.o(.text.PID_INIT)
    PendSV_Handler                           0x0800500d   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08005011   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    Step_Motor_Init                          0x08005015   Thumb Code    66  step_motor_bsp.o(.text.Step_Motor_Init)
    Step_Motor_Set_Speed_my                  0x08005059   Thumb Code   210  step_motor_bsp.o(.text.Step_Motor_Set_Speed_my)
    SysTick_Handler                          0x0800512d   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08005131   Thumb Code   164  main.o(.text.SystemClock_Config)
    SystemInit                               0x080051d5   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x080051e9   Thumb Code   314  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART4_IRQHandler                         0x08005325   Thumb Code    52  stm32f4xx_it.o(.text.UART4_IRQHandler)
    USART1_IRQHandler                        0x08005841   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x0800584d   Thumb Code    52  stm32f4xx_it.o(.text.USART2_IRQHandler)
    USART3_IRQHandler                        0x08005881   Thumb Code    12  stm32f4xx_it.o(.text.USART3_IRQHandler)
    USART6_IRQHandler                        0x0800588d   Thumb Code    52  stm32f4xx_it.o(.text.USART6_IRQHandler)
    UsageFault_Handler                       0x080058c1   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    main                                     0x080058c5   Thumb Code   192  main.o(.text.main)
    my_printf                                0x08005989   Thumb Code    64  uart_bsp.o(.text.my_printf)
    parse_x_motor_data                       0x080059c9   Thumb Code   792  uart_bsp.o(.text.parse_x_motor_data)
    parse_y_motor_data                       0x08005d75   Thumb Code   792  uart_bsp.o(.text.parse_y_motor_data)
    pi_parse_data                            0x08006121   Thumb Code   168  pi_bsp.o(.text.pi_parse_data)
    rt_ringbuffer_data_len                   0x08006249   Thumb Code    68  ringbuffer.o(.text.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x0800628d   Thumb Code   210  ringbuffer.o(.text.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08006361   Thumb Code    56  ringbuffer.o(.text.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080063b1   Thumb Code   206  ringbuffer.o(.text.rt_ringbuffer_put)
    save_initial_position                    0x08006481   Thumb Code    68  uart_bsp.o(.text.save_initial_position)
    schedule_init                            0x080064e5   Thumb Code    14  schedule.o(.text.schedule_init)
    schedule_run                             0x080064f5   Thumb Code    74  schedule.o(.text.schedule_run)
    uart_proc                                0x08006541   Thumb Code   376  uart_bsp.o(.text.uart_proc)
    _btod_d2e                                0x08006739   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08006777   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080067bd   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800681d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08006b55   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08006c31   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08006c5b   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08006c85   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08006ec9   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08006ef9   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x08006f09   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08006f35   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __fpl_dretinf                            0x08006f61   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08006f6d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08006f6d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08006fc3   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800704f   Thumb Code    26  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08007067   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08007067   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08007069   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800706d   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08007070   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800716c   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x0800717c   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x0800763c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800765c   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08007685   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    latest_red_laser_coord                   0x20000008   Data          16  pi_bsp.o(.data..L_MergedGlobals)
    latest_green_laser_coord                 0x20000018   Data          16  pi_bsp.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000028   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    __libspace_start                         0x20000038   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000098   Data           0  libspace.o(.bss)
    x_angle_limit_flag                       0x20000098   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    y_angle_limit_flag                       0x20000099   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    motor_angle_limit_check_enabled          0x2000009a   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    x_reference_initialized                  0x2000009b   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    y_reference_initialized                  0x2000009c   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    x_initial_direction                      0x2000009d   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    y_initial_direction                      0x2000009e   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    initial_position_saved                   0x2000009f   Data           1  uart_bsp.o(.bss..L_MergedGlobals)
    x_motor_angle                            0x200000a0   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    y_motor_angle                            0x200000a4   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    x_reference_position                     0x200000a8   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    y_reference_position                     0x200000ac   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    x_relative_angle                         0x200000b0   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    y_relative_angle                         0x200000b4   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    x_initial_position                       0x200000b8   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    y_initial_position                       0x200000bc   Data           4  uart_bsp.o(.bss..L_MergedGlobals)
    ringbuffer_x                             0x200000c4   Data          12  uart_bsp.o(.bss..L_MergedGlobals)
    ringbuffer_y                             0x200000d0   Data          12  uart_bsp.o(.bss..L_MergedGlobals)
    ringbuffer_pi                            0x200000dc   Data          12  uart_bsp.o(.bss..L_MergedGlobals)
    hdma_uart4_rx                            0x200000e8   Data          96  usart.o(.bss.hdma_uart4_rx)
    hdma_usart2_rx                           0x20000148   Data          96  usart.o(.bss.hdma_usart2_rx)
    hdma_usart6_rx                           0x200001a8   Data          96  usart.o(.bss.hdma_usart6_rx)
    hi2c1                                    0x20000208   Data          84  i2c.o(.bss.hi2c1)
    hi2c2                                    0x2000025c   Data          84  i2c.o(.bss.hi2c2)
    htim1                                    0x200002b0   Data          72  tim.o(.bss.htim1)
    htim3                                    0x200002f8   Data          72  tim.o(.bss.htim3)
    htim4                                    0x20000340   Data          72  tim.o(.bss.htim4)
    huart1                                   0x20000388   Data          72  usart.o(.bss.huart1)
    huart2                                   0x200003d0   Data          72  usart.o(.bss.huart2)
    huart3                                   0x20000418   Data          72  usart.o(.bss.huart3)
    huart4                                   0x20000460   Data          72  usart.o(.bss.huart4)
    huart5                                   0x200004a8   Data          72  usart.o(.bss.huart5)
    huart6                                   0x200004f0   Data          72  usart.o(.bss.huart6)
    motor_x_buf                              0x200005b8   Data          64  usart.o(.bss.motor_x_buf)
    motor_y_buf                              0x200005f8   Data          64  usart.o(.bss.motor_y_buf)
    output_buffer_pi                         0x20000638   Data          64  uart_bsp.o(.bss.output_buffer_pi)
    output_buffer_x                          0x20000678   Data          64  uart_bsp.o(.bss.output_buffer_x)
    output_buffer_y                          0x200006b8   Data          64  uart_bsp.o(.bss.output_buffer_y)
    pi_rx_buf                                0x200006f8   Data          64  usart.o(.bss.pi_rx_buf)
    pid_location_left                        0x20000738   Data          80  mypid.o(.bss.pid_location_left)
    pid_location_right                       0x20000788   Data          80  mypid.o(.bss.pid_location_right)
    pid_speed_left                           0x200007d8   Data          80  mypid.o(.bss.pid_speed_left)
    pid_speed_right                          0x20000828   Data          80  mypid.o(.bss.pid_speed_right)
    pid_x                                    0x20000878   Data          80  mypid.o(.bss.pid_x)
    pid_y                                    0x200008c8   Data          80  mypid.o(.bss.pid_y)
    ringbuffer_pool_pi                       0x20000918   Data          64  uart_bsp.o(.bss.ringbuffer_pool_pi)
    ringbuffer_pool_x                        0x20000958   Data          64  uart_bsp.o(.bss.ringbuffer_pool_x)
    ringbuffer_pool_y                        0x20000998   Data          64  uart_bsp.o(.bss.ringbuffer_pool_y)
    task_num                                 0x200009d8   Data           1  schedule.o(.bss.task_num)
    uwTick                                   0x200009dc   Data           4  stm32f4xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000077c0, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007788, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1438  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x0000005c   Code   RO         1858    !!!scatter          c_w.l(__scatter.o)
    0x080001ec   0x080001ec   0x0000001a   Code   RO         1862    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000206   0x08000206   0x00000002   PAD
    0x08000208   0x08000208   0x00000002   Code   RO         1859    !!handler_null      c_w.l(__scatter.o)
    0x0800020a   0x0800020a   0x00000002   PAD
    0x0800020c   0x0800020c   0x0000001c   Code   RO         1864    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000228   0x08000228   0x00000000   Code   RO         1538    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000228   0x08000228   0x00000006   Code   RO         1527    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x0800022e   0x0800022e   0x00000006   Code   RO         1529    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000234   0x08000234   0x00000006   Code   RO         1534    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800023a   0x0800023a   0x00000006   Code   RO         1535    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000240   0x08000240   0x00000006   Code   RO         1536    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x08000246   0x08000246   0x00000006   Code   RO         1537    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x0800024c   0x0800024c   0x0000000a   Code   RO         1542    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x08000256   0x08000256   0x00000006   Code   RO         1531    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x0800025c   0x0800025c   0x00000006   Code   RO         1532    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000262   0x08000262   0x00000006   Code   RO         1533    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x08000268   0x08000268   0x00000006   Code   RO         1530    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x0800026e   0x0800026e   0x00000006   Code   RO         1528    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000274   0x08000274   0x00000006   Code   RO         1539    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800027a   0x0800027a   0x00000006   Code   RO         1540    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000280   0x08000280   0x00000006   Code   RO         1541    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x08000286   0x08000286   0x00000006   Code   RO         1546    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x0800028c   0x0800028c   0x00000006   Code   RO         1547    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000292   0x08000292   0x0000000a   Code   RO         1543    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x0800029c   0x0800029c   0x00000006   Code   RO         1525    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002a2   0x080002a2   0x00000006   Code   RO         1526    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002a8   0x080002a8   0x00000006   Code   RO         1544    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002ae   0x080002ae   0x00000006   Code   RO         1545    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002b4   0x080002b4   0x00000004   Code   RO         1648    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002b8   0x080002b8   0x00000002   Code   RO         1727    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         1747    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         1750    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         1752    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         1755    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         1757    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         1759    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x080002be   0x080002be   0x00000006   Code   RO         1760    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002c4   0x080002c4   0x00000000   Code   RO         1762    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002c4   0x080002c4   0x0000000c   Code   RO         1763    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x00000000   Code   RO         1764    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x00000000   Code   RO         1766    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002d0   0x080002d0   0x0000000a   Code   RO         1767    .ARM.Collect$$libinit$$00000018  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1768    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1770    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1772    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1774    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1776    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1778    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1780    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1782    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1786    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1788    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1790    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         1792    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000002   Code   RO         1793    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x080002dc   0x080002dc   0x00000002   Code   RO         1829    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1841    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1843    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1846    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1849    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1851    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000000   Code   RO         1854    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002de   0x080002de   0x00000002   Code   RO         1855    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO         1450    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO         1608    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002e0   0x080002e0   0x00000006   Code   RO         1620    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002e6   0x080002e6   0x00000000   Code   RO         1610    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002e6   0x080002e6   0x00000004   Code   RO         1611    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000000   Code   RO         1613    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002ea   0x080002ea   0x00000008   Code   RO         1614    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002f2   0x080002f2   0x00000002   Code   RO         1732    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         1795    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002f4   0x080002f4   0x00000004   Code   RO         1796    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002f8   0x080002f8   0x00000006   Code   RO         1797    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002fe   0x080002fe   0x00000002   PAD
    0x08000300   0x08000300   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000340   0x08000340   0x000000f0   Code   RO         1420    .text               c_w.l(lludivv7m.o)
    0x08000430   0x08000430   0x0000003c   Code   RO         1422    .text               c_w.l(vsnprintf.o)
    0x0800046c   0x0800046c   0x0000003c   Code   RO         1424    .text               c_w.l(__0sscanf.o)
    0x080004a8   0x080004a8   0x0000014c   Code   RO         1426    .text               c_w.l(_scanf_int.o)
    0x080005f4   0x080005f4   0x00000080   Code   RO         1428    .text               c_w.l(assert.o)
    0x08000674   0x08000674   0x00000096   Code   RO         1430    .text               c_w.l(strncmp.o)
    0x0800070a   0x0800070a   0x0000008a   Code   RO         1432    .text               c_w.l(rt_memcpy_v6.o)
    0x08000794   0x08000794   0x0000004e   Code   RO         1434    .text               c_w.l(rt_memclr_w.o)
    0x080007e2   0x080007e2   0x00000006   Code   RO         1436    .text               c_w.l(heapauxi.o)
    0x080007e8   0x080007e8   0x0000004e   Code   RO         1459    .text               c_w.l(_printf_pad.o)
    0x08000836   0x08000836   0x00000024   Code   RO         1461    .text               c_w.l(_printf_truncate.o)
    0x0800085a   0x0800085a   0x00000052   Code   RO         1463    .text               c_w.l(_printf_str.o)
    0x080008ac   0x080008ac   0x00000078   Code   RO         1465    .text               c_w.l(_printf_dec.o)
    0x08000924   0x08000924   0x00000028   Code   RO         1467    .text               c_w.l(_printf_charcount.o)
    0x0800094c   0x0800094c   0x00000030   Code   RO         1469    .text               c_w.l(_printf_char_common.o)
    0x0800097c   0x0800097c   0x00000010   Code   RO         1471    .text               c_w.l(_snputc.o)
    0x0800098c   0x0800098c   0x000000bc   Code   RO         1473    .text               c_w.l(_printf_wctomb.o)
    0x08000a48   0x08000a48   0x0000007c   Code   RO         1476    .text               c_w.l(_printf_longlong_dec.o)
    0x08000ac4   0x08000ac4   0x00000070   Code   RO         1482    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000b34   0x08000b34   0x00000094   Code   RO         1502    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000bc8   0x08000bc8   0x00000188   Code   RO         1522    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000d50   0x08000d50   0x0000001c   Code   RO         1548    .text               c_w.l(_chval.o)
    0x08000d6c   0x08000d6c   0x0000002c   Code   RO         1550    .text               c_w.l(scanf_char.o)
    0x08000d98   0x08000d98   0x00000040   Code   RO         1552    .text               c_w.l(_sgetc.o)
    0x08000dd8   0x08000dd8   0x00000016   Code   RO         1554    .text               c_w.l(abort.o)
    0x08000dee   0x08000dee   0x00000014   Code   RO         1556    .text               c_w.l(assert_puts.o)
    0x08000e02   0x08000e02   0x00000064   Code   RO         1558    .text               c_w.l(rt_memcpy_w.o)
    0x08000e66   0x08000e66   0x0000000e   Code   RO         1599    .text               c_w.l(sys_wrch.o)
    0x08000e74   0x08000e74   0x0000000c   Code   RO         1601    .text               c_w.l(sys_exit.o)
    0x08000e80   0x08000e80   0x0000008a   Code   RO         1629    .text               c_w.l(lludiv10.o)
    0x08000f0a   0x08000f0a   0x00000012   Code   RO         1631    .text               c_w.l(isspace.o)
    0x08000f1c   0x08000f1c   0x000000b2   Code   RO         1633    .text               c_w.l(_printf_intcommon.o)
    0x08000fce   0x08000fce   0x0000041c   Code   RO         1635    .text               c_w.l(_printf_fp_dec.o)
    0x080013ea   0x080013ea   0x00000002   PAD
    0x080013ec   0x080013ec   0x000002fc   Code   RO         1639    .text               c_w.l(_printf_fp_hex.o)
    0x080016e8   0x080016e8   0x0000002c   Code   RO         1644    .text               c_w.l(_printf_char.o)
    0x08001714   0x08001714   0x0000002c   Code   RO         1646    .text               c_w.l(_printf_wchar.o)
    0x08001740   0x08001740   0x00000374   Code   RO         1649    .text               c_w.l(_scanf.o)
    0x08001ab4   0x08001ab4   0x00000048   Code   RO         1651    .text               c_w.l(_c16rtomb.o)
    0x08001afc   0x08001afc   0x0000000e   Code   RO         1653    .text               c_w.l(defsig_abrt_outer.o)
    0x08001b0a   0x08001b0a   0x00000002   PAD
    0x08001b0c   0x08001b0c   0x00000008   Code   RO         1663    .text               c_w.l(libspace.o)
    0x08001b14   0x08001b14   0x00000002   Code   RO         1666    .text               c_w.l(use_no_semi.o)
    0x08001b16   0x08001b16   0x00000000   Code   RO         1668    .text               c_w.l(indicate_semi.o)
    0x08001b16   0x08001b16   0x0000004a   Code   RO         1669    .text               c_w.l(sys_stackheap_outer.o)
    0x08001b60   0x08001b60   0x00000010   Code   RO         1671    .text               c_w.l(rt_ctype_table.o)
    0x08001b70   0x08001b70   0x00000008   Code   RO         1678    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001b78   0x08001b78   0x00000080   Code   RO         1680    .text               c_w.l(_printf_fp_infnan.o)
    0x08001bf8   0x08001bf8   0x000000e4   Code   RO         1682    .text               c_w.l(bigflt0.o)
    0x08001cdc   0x08001cdc   0x00000012   Code   RO         1712    .text               c_w.l(exit.o)
    0x08001cee   0x08001cee   0x0000000a   Code   RO         1714    .text               c_w.l(defsig_exit.o)
    0x08001cf8   0x08001cf8   0x00000030   Code   RO         1716    .text               c_w.l(defsig_abrt_inner.o)
    0x08001d28   0x08001d28   0x00000032   Code   RO         1741    .text               c_w.l(defsig_general.o)
    0x08001d5a   0x08001d5a   0x00000006   PAD
    0x08001d60   0x08001d60   0x0000007c   Code   RO         1745    .text               c_w.l(strcmpv7em.o)
    0x08001ddc   0x08001ddc   0x00000002   Code   RO          134    .text.BusFault_Handler  stm32f4xx_it.o
    0x08001dde   0x08001dde   0x00000002   PAD
    0x08001de0   0x08001de0   0x0000000c   Code   RO          146    .text.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x08001dec   0x08001dec   0x0000000c   Code   RO          148    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08001df8   0x08001df8   0x0000000c   Code   RO          158    .text.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08001e04   0x08001e04   0x00000002   Code   RO          140    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08001e06   0x08001e06   0x00000002   PAD
    0x08001e08   0x08001e08   0x0000003e   Code   RO         1346    .text.Emm_V5_En_Control  emm_v5.o
    0x08001e46   0x08001e46   0x00000002   PAD
    0x08001e48   0x08001e48   0x0000032a   Code   RO         1364    .text.Emm_V5_Parse_Response  emm_v5.o
    0x08002172   0x08002172   0x00000002   PAD
    0x08002174   0x08002174   0x000000d8   Code   RO         1342    .text.Emm_V5_Read_Sys_Params  emm_v5.o
    0x0800224c   0x0800224c   0x0000002e   Code   RO         1338    .text.Emm_V5_Reset_CurPos_To_Zero  emm_v5.o
    0x0800227a   0x0800227a   0x00000002   PAD
    0x0800227c   0x0800227c   0x00000036   Code   RO         1352    .text.Emm_V5_Stop_Now  emm_v5.o
    0x080022b2   0x080022b2   0x00000002   PAD
    0x080022b4   0x080022b4   0x00000120   Code   RO         1348    .text.Emm_V5_Vel_Control  emm_v5.o
    0x080023d4   0x080023d4   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x080023da   0x080023da   0x00000002   PAD
    0x080023dc   0x080023dc   0x0000008e   Code   RO          472    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x0800246a   0x0800246a   0x00000002   PAD
    0x0800246c   0x0800246c   0x00000024   Code   RO          474    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002490   0x08002490   0x000001c4   Code   RO          478    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002654   0x08002654   0x00000162   Code   RO          464    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x080027b6   0x080027b6   0x00000002   PAD
    0x080027b8   0x080027b8   0x000000a2   Code   RO          470    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800285a   0x0800285a   0x00000002   PAD
    0x0800285c   0x0800285c   0x0000019e   Code   RO          426    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x080029fa   0x080029fa   0x00000002   PAD
    0x080029fc   0x080029fc   0x0000000a   Code   RO          432    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002a06   0x08002a06   0x00000002   PAD
    0x08002a08   0x08002a08   0x0000000c   Code   RO          623    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08002a14   0x08002a14   0x00000164   Code   RO          177    .text.HAL_I2C_Init  stm32f4xx_hal_i2c.o
    0x08002b78   0x08002b78   0x000000ce   Code   RO           47    .text.HAL_I2C_MspInit  i2c.o
    0x08002c46   0x08002c46   0x00000002   PAD
    0x08002c48   0x08002c48   0x0000001a   Code   RO          621    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08002c62   0x08002c62   0x00000002   PAD
    0x08002c64   0x08002c64   0x00000036   Code   RO          611    .text.HAL_Init      stm32f4xx_hal.o
    0x08002c9a   0x08002c9a   0x00000002   PAD
    0x08002c9c   0x08002c9c   0x00000048   Code   RO          613    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08002ce4   0x08002ce4   0x00000038   Code   RO          169    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08002d1c   0x08002d1c   0x00000022   Code   RO          563    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002d3e   0x08002d3e   0x00000002   PAD
    0x08002d40   0x08002d40   0x00000056   Code   RO          561    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002d96   0x08002d96   0x00000002   PAD
    0x08002d98   0x08002d98   0x00000020   Code   RO          559    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002db8   0x08002db8   0x00000164   Code   RO          320    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002f1c   0x08002f1c   0x00000026   Code   RO          332    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002f42   0x08002f42   0x00000002   PAD
    0x08002f44   0x08002f44   0x00000026   Code   RO          334    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002f6a   0x08002f6a   0x00000002   PAD
    0x08002f6c   0x08002f6c   0x0000006a   Code   RO          322    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002fd6   0x08002fd6   0x00000002   PAD
    0x08002fd8   0x08002fd8   0x000003ac   Code   RO          318    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003384   0x08003384   0x0000002c   Code   RO          571    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080033b0   0x080033b0   0x0000004c   Code   RO          998    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x080033fc   0x080033fc   0x000000ba   Code   RO          996    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080034b6   0x080034b6   0x00000002   PAD
    0x080034b8   0x080034b8   0x0000005a   Code   RO          701    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003512   0x08003512   0x00000002   PAD
    0x08003514   0x08003514   0x00000032   Code   RO           69    .text.HAL_TIM_Base_MspInit  tim.o
    0x08003546   0x08003546   0x00000002   PAD
    0x08003548   0x08003548   0x000001a0   Code   RO          881    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x080036e8   0x080036e8   0x000000b2   Code   RO          815    .text.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x0800379a   0x0800379a   0x00000002   PAD
    0x0800379c   0x0800379c   0x000000c0   Code   RO           71    .text.HAL_TIM_Encoder_MspInit  tim.o
    0x0800385c   0x0800385c   0x00000220   Code   RO          855    .text.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003a7c   0x08003a7c   0x0000005a   Code   RO          755    .text.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003ad6   0x08003ad6   0x00000002   PAD
    0x08003ad8   0x08003ad8   0x00000002   Code   RO          757    .text.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003ada   0x08003ada   0x00000002   PAD
    0x08003adc   0x08003adc   0x000001ee   Code   RO         1068    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003cca   0x08003cca   0x00000002   PAD
    0x08003ccc   0x08003ccc   0x000000a4   Code   RO           88    .text.HAL_UARTEx_RxEventCallback  usart.o
    0x08003d70   0x08003d70   0x00000002   Code   RO         1104    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003d72   0x08003d72   0x00000002   PAD
    0x08003d74   0x08003d74   0x00000594   Code   RO         1098    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08004308   0x08004308   0x00000060   Code   RO         1020    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x08004368   0x08004368   0x000003ac   Code   RO          102    .text.HAL_UART_MspInit  usart.o
    0x08004714   0x08004714   0x00000002   Code   RO         1112    .text.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08004716   0x08004716   0x00000002   PAD
    0x08004718   0x08004718   0x00000002   Code   RO         1114    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800471a   0x0800471a   0x00000002   PAD
    0x0800471c   0x0800471c   0x00000192   Code   RO         1036    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080048ae   0x080048ae   0x00000002   PAD
    0x080048b0   0x080048b0   0x00000002   Code   RO         1108    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080048b2   0x080048b2   0x00000002   PAD
    0x080048b4   0x080048b4   0x00000002   Code   RO          130    .text.HardFault_Handler  stm32f4xx_it.o
    0x080048b6   0x080048b6   0x00000002   PAD
    0x080048b8   0x080048b8   0x0000006c   Code   RO           34    .text.MX_DMA_Init   dma.o
    0x08004924   0x08004924   0x00000140   Code   RO           25    .text.MX_GPIO_Init  gpio.o
    0x08004a64   0x08004a64   0x00000042   Code   RO           43    .text.MX_I2C1_Init  i2c.o
    0x08004aa6   0x08004aa6   0x00000002   PAD
    0x08004aa8   0x08004aa8   0x00000042   Code   RO           45    .text.MX_I2C2_Init  i2c.o
    0x08004aea   0x08004aea   0x00000002   PAD
    0x08004aec   0x08004aec   0x00000166   Code   RO           61    .text.MX_TIM1_Init  tim.o
    0x08004c52   0x08004c52   0x00000002   PAD
    0x08004c54   0x08004c54   0x00000068   Code   RO           65    .text.MX_TIM3_Init  tim.o
    0x08004cbc   0x08004cbc   0x00000068   Code   RO           67    .text.MX_TIM4_Init  tim.o
    0x08004d24   0x08004d24   0x00000060   Code   RO           90    .text.MX_UART4_Init  usart.o
    0x08004d84   0x08004d84   0x0000003c   Code   RO           92    .text.MX_UART5_Init  usart.o
    0x08004dc0   0x08004dc0   0x0000003c   Code   RO           94    .text.MX_USART1_UART_Init  usart.o
    0x08004dfc   0x08004dfc   0x00000060   Code   RO           96    .text.MX_USART2_UART_Init  usart.o
    0x08004e5c   0x08004e5c   0x0000003c   Code   RO           98    .text.MX_USART3_UART_Init  usart.o
    0x08004e98   0x08004e98   0x00000060   Code   RO          100    .text.MX_USART6_UART_Init  usart.o
    0x08004ef8   0x08004ef8   0x00000002   Code   RO          132    .text.MemManage_Handler  stm32f4xx_it.o
    0x08004efa   0x08004efa   0x00000002   PAD
    0x08004efc   0x08004efc   0x00000002   Code   RO          128    .text.NMI_Handler   stm32f4xx_it.o
    0x08004efe   0x08004efe   0x00000002   PAD
    0x08004f00   0x08004f00   0x0000010a   Code   RO         1375    .text.PID_INIT      mypid.o
    0x0800500a   0x0800500a   0x00000002   PAD
    0x0800500c   0x0800500c   0x00000002   Code   RO          142    .text.PendSV_Handler  stm32f4xx_it.o
    0x0800500e   0x0800500e   0x00000002   PAD
    0x08005010   0x08005010   0x00000002   Code   RO          138    .text.SVC_Handler   stm32f4xx_it.o
    0x08005012   0x08005012   0x00000002   PAD
    0x08005014   0x08005014   0x00000042   Code   RO         1305    .text.Step_Motor_Init  step_motor_bsp.o
    0x08005056   0x08005056   0x00000002   PAD
    0x08005058   0x08005058   0x000000d2   Code   RO         1311    .text.Step_Motor_Set_Speed_my  step_motor_bsp.o
    0x0800512a   0x0800512a   0x00000002   PAD
    0x0800512c   0x0800512c   0x00000004   Code   RO          144    .text.SysTick_Handler  stm32f4xx_it.o
    0x08005130   0x08005130   0x000000a4   Code   RO           13    .text.SystemClock_Config  main.o
    0x080051d4   0x080051d4   0x00000012   Code   RO         1142    .text.SystemInit    system_stm32f4xx.o
    0x080051e6   0x080051e6   0x00000002   PAD
    0x080051e8   0x080051e8   0x0000013a   Code   RO          705    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005322   0x08005322   0x00000002   PAD
    0x08005324   0x08005324   0x00000034   Code   RO          156    .text.UART4_IRQHandler  stm32f4xx_it.o
    0x08005358   0x08005358   0x0000000a   Code   RO         1102    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005362   0x08005362   0x00000002   PAD
    0x08005364   0x08005364   0x0000017c   Code   RO         1052    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x080054e0   0x080054e0   0x0000015e   Code   RO         1130    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800563e   0x0800563e   0x00000002   PAD
    0x08005640   0x08005640   0x00000018   Code   RO         1132    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08005658   0x08005658   0x000000fe   Code   RO         1100    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08005756   0x08005756   0x00000002   PAD
    0x08005758   0x08005758   0x000000e6   Code   RO         1024    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x0800583e   0x0800583e   0x00000002   PAD
    0x08005840   0x08005840   0x0000000c   Code   RO          150    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x0800584c   0x0800584c   0x00000034   Code   RO          152    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x08005880   0x08005880   0x0000000c   Code   RO          154    .text.USART3_IRQHandler  stm32f4xx_it.o
    0x0800588c   0x0800588c   0x00000034   Code   RO          160    .text.USART6_IRQHandler  stm32f4xx_it.o
    0x080058c0   0x080058c0   0x00000002   Code   RO          136    .text.UsageFault_Handler  stm32f4xx_it.o
    0x080058c2   0x080058c2   0x00000002   PAD
    0x080058c4   0x080058c4   0x000000c4   Code   RO           11    .text.main          main.o
    0x08005988   0x08005988   0x00000040   Code   RO         1268    .text.my_printf     uart_bsp.o
    0x080059c8   0x080059c8   0x000003ac   Code   RO         1276    .text.parse_x_motor_data  uart_bsp.o
    0x08005d74   0x08005d74   0x000003ac   Code   RO         1278    .text.parse_y_motor_data  uart_bsp.o
    0x08006120   0x08006120   0x00000108   Code   RO         1325    .text.pi_parse_data  pi_bsp.o
    0x08006228   0x08006228   0x0000000e   Code   RO         1393    .text.pid_param_init  mypid.o
    0x08006236   0x08006236   0x00000002   PAD
    0x08006238   0x08006238   0x00000010   Code   RO         1395    .text.pid_reset     mypid.o
    0x08006248   0x08006248   0x00000044   Code   RO         1204    .text.rt_ringbuffer_data_len  ringbuffer.o
    0x0800628c   0x0800628c   0x000000d2   Code   RO         1208    .text.rt_ringbuffer_get  ringbuffer.o
    0x0800635e   0x0800635e   0x00000002   PAD
    0x08006360   0x08006360   0x00000050   Code   RO         1200    .text.rt_ringbuffer_init  ringbuffer.o
    0x080063b0   0x080063b0   0x000000ce   Code   RO         1202    .text.rt_ringbuffer_put  ringbuffer.o
    0x0800647e   0x0800647e   0x00000002   PAD
    0x08006480   0x08006480   0x00000064   Code   RO         1282    .text.save_initial_position  uart_bsp.o
    0x080064e4   0x080064e4   0x0000000e   Code   RO         1229    .text.schedule_init  schedule.o
    0x080064f2   0x080064f2   0x00000002   PAD
    0x080064f4   0x080064f4   0x0000004a   Code   RO         1231    .text.schedule_run  schedule.o
    0x0800653e   0x0800653e   0x00000002   PAD
    0x08006540   0x08006540   0x000001f8   Code   RO         1286    .text.uart_proc     uart_bsp.o
    0x08006738   0x08006738   0x0000003e   Code   RO         1685    CL$$btod_d2e        c_w.l(btod.o)
    0x08006776   0x08006776   0x00000046   Code   RO         1687    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080067bc   0x080067bc   0x00000060   Code   RO         1686    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800681c   0x0800681c   0x00000338   Code   RO         1695    CL$$btod_div_common  c_w.l(btod.o)
    0x08006b54   0x08006b54   0x000000dc   Code   RO         1692    CL$$btod_e2e        c_w.l(btod.o)
    0x08006c30   0x08006c30   0x0000002a   Code   RO         1689    CL$$btod_ediv       c_w.l(btod.o)
    0x08006c5a   0x08006c5a   0x0000002a   Code   RO         1688    CL$$btod_emul       c_w.l(btod.o)
    0x08006c84   0x08006c84   0x00000244   Code   RO         1694    CL$$btod_mult_common  c_w.l(btod.o)
    0x08006ec8   0x08006ec8   0x00000030   Code   RO         1730    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08006ef8   0x08006ef8   0x0000000e   Code   RO         1515    i._is_digit         c_w.l(__printf_wp.o)
    0x08006f06   0x08006f06   0x00000002   PAD
    0x08006f08   0x08006f08   0x0000002c   Code   RO         1710    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006f34   0x08006f34   0x0000002c   Code   RO         1737    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006f60   0x08006f60   0x0000000c   Code   RO         1570    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08006f6c   0x08006f6c   0x00000056   Code   RO         1442    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08006fc2   0x08006fc2   0x0000008c   Code   RO         1572    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800704e   0x0800704e   0x0000001a   Code   RO         1807    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08007068   0x08007068   0x00000004   Code   RO         1576    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800706c   0x0800706c   0x00000004   Code   RO         1578    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08007070   0x08007070   0x00000000   Code   RO         1584    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08007070   0x08007070   0x00000008   Data   RO         1474    .constdata          c_w.l(_printf_wctomb.o)
    0x08007078   0x08007078   0x00000028   Data   RO         1503    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080070a0   0x080070a0   0x00000011   Data   RO         1523    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080070b1   0x080070b1   0x00000026   Data   RO         1640    .constdata          c_w.l(_printf_fp_hex.o)
    0x080070d7   0x080070d7   0x00000001   PAD
    0x080070d8   0x080070d8   0x00000094   Data   RO         1683    .constdata          c_w.l(bigflt0.o)
    0x0800716c   0x0800716c   0x00000010   Data   RO         1147    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x0800717c   0x0800717c   0x00000008   Data   RO         1148    .rodata.APBPrescTable  system_stm32f4xx.o
    0x08007184   0x08007184   0x00000008   Data   RO          488    .rodata.cst8        stm32f4xx_hal_dma.o
    0x0800718c   0x0800718c   0x00000029   Data   RO         1220    .rodata.str1.1      ringbuffer.o
    0x080071b5   0x080071b5   0x0000047c   Data   RO         1288    .rodata.str1.1      uart_bsp.o
    0x08007631   0x08007631   0x00000008   Data   RO         1366    .rodata.str1.1      emm_v5.o
    0x08007639   0x08007639   0x00000003   PAD
    0x0800763c   0x0800763c   0x00000020   Data   RO         1857    Region$$Table       anon$$obj.o
    0x0800765c   0x0800765c   0x0000001c   Data   RO         1709    locale$$data        c_w.l(lc_numeric_c.o)
    0x08007678   0x08007678   0x00000110   Data   RO         1736    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007788, Size: 0x00000fe0, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007788   0x00000008   Data   RW          666    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000008   0x08007790   0x00000020   Data   RW         1329    .data..L_MergedGlobals  pi_bsp.o
    0x20000028   0x080077b0   0x00000004   Data   RW         1146    .data.SystemCoreClock  system_stm32f4xx.o
    0x2000002c   0x080077b4   0x0000000c   Data   RW         1234    .data.schedule_task  schedule.o
    0x20000038        -       0x00000060   Zero   RW         1664    .bss                c_w.l(libspace.o)
    0x20000098        -       0x00000050   Zero   RW         1296    .bss..L_MergedGlobals  uart_bsp.o
    0x200000e8        -       0x00000060   Zero   RW          110    .bss.hdma_uart4_rx  usart.o
    0x20000148        -       0x00000060   Zero   RW          114    .bss.hdma_usart2_rx  usart.o
    0x200001a8        -       0x00000060   Zero   RW          117    .bss.hdma_usart6_rx  usart.o
    0x20000208        -       0x00000054   Zero   RW           51    .bss.hi2c1          i2c.o
    0x2000025c        -       0x00000054   Zero   RW           52    .bss.hi2c2          i2c.o
    0x200002b0        -       0x00000048   Zero   RW           77    .bss.htim1          tim.o
    0x200002f8        -       0x00000048   Zero   RW           78    .bss.htim3          tim.o
    0x20000340        -       0x00000048   Zero   RW           79    .bss.htim4          tim.o
    0x20000388        -       0x00000048   Zero   RW          112    .bss.huart1         usart.o
    0x200003d0        -       0x00000048   Zero   RW          113    .bss.huart2         usart.o
    0x20000418        -       0x00000048   Zero   RW          115    .bss.huart3         usart.o
    0x20000460        -       0x00000048   Zero   RW          109    .bss.huart4         usart.o
    0x200004a8        -       0x00000048   Zero   RW          111    .bss.huart5         usart.o
    0x200004f0        -       0x00000048   Zero   RW          116    .bss.huart6         usart.o
    0x20000538        -       0x00000080   Zero   RW         1292    .bss.line_buffer    uart_bsp.o
    0x200005b8        -       0x00000040   Zero   RW          106    .bss.motor_x_buf    usart.o
    0x200005f8        -       0x00000040   Zero   RW          107    .bss.motor_y_buf    usart.o
    0x20000638        -       0x00000040   Zero   RW         1291    .bss.output_buffer_pi  uart_bsp.o
    0x20000678        -       0x00000040   Zero   RW         1289    .bss.output_buffer_x  uart_bsp.o
    0x200006b8        -       0x00000040   Zero   RW         1290    .bss.output_buffer_y  uart_bsp.o
    0x200006f8        -       0x00000040   Zero   RW          108    .bss.pi_rx_buf      usart.o
    0x20000738        -       0x00000050   Zero   RW         1403    .bss.pid_location_left  mypid.o
    0x20000788        -       0x00000050   Zero   RW         1404    .bss.pid_location_right  mypid.o
    0x200007d8        -       0x00000050   Zero   RW         1401    .bss.pid_speed_left  mypid.o
    0x20000828        -       0x00000050   Zero   RW         1402    .bss.pid_speed_right  mypid.o
    0x20000878        -       0x00000050   Zero   RW         1399    .bss.pid_x          mypid.o
    0x200008c8        -       0x00000050   Zero   RW         1400    .bss.pid_y          mypid.o
    0x20000918        -       0x00000040   Zero   RW         1295    .bss.ringbuffer_pool_pi  uart_bsp.o
    0x20000958        -       0x00000040   Zero   RW         1293    .bss.ringbuffer_pool_x  uart_bsp.o
    0x20000998        -       0x00000040   Zero   RW         1294    .bss.ringbuffer_pool_y  uart_bsp.o
    0x200009d8        -       0x00000001   Zero   RW         1233    .bss.task_num       schedule.o
    0x200009d9   0x080077c0   0x00000003   PAD
    0x200009dc        -       0x00000004   Zero   RW          665    .bss.uwTick         stm32f4xx_hal.o
    0x200009e0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000be0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x080077c0, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       108          0          0          0          0       3489   dma.o
      1476        166          8          0          0      13837   emm_v5.o
       320          0          0          0          0       2250   gpio.o
       338          0          0          0        168       5460   i2c.o
       366          4          0          0          0       3413   main.o
       296          0          0          0        480      10129   mypid.o
       264         96          0         32          0       2243   pi_bsp.o
       564         24         41          0          0       7027   ringbuffer.o
        88          0          0         12          1       1176   schedule.o
        64         26        392          0       1536        832   startup_stm32f407xx.o
       276          0          0          0          0       5381   step_motor_bsp.o
       164          0          0          8          4       7285   stm32f4xx_hal.o
       196          0          0          0          0      10702   stm32f4xx_hal_cortex.o
      1146          6          8          0          0      10514   stm32f4xx_hal_dma.o
       424          0          0          0          0       5332   stm32f4xx_hal_gpio.o
       356          0          0          0          0      44386   stm32f4xx_hal_i2c.o
        56          0          0          0          0       1440   stm32f4xx_hal_msp.o
      1478          0          0          0          0       7392   stm32f4xx_hal_rcc.o
      1634          6          0          0          0      57410   stm32f4xx_hal_tim.o
       262          0          0          0          0      21467   stm32f4xx_hal_tim_ex.o
      3676          0          0          0          0      32225   stm32f4xx_hal_uart.o
       236          0          0          0          0       4836   stm32f4xx_it.o
        18          0         24          4          0       2569   system_stm32f4xx.o
       808          0          0          0        216       9450   tim.o
      2548        584       1148          0        592      12260   uart_bsp.o
      1572          0          0          0        912       9355   usart.o

    ----------------------------------------------------------------------
     18844        <USER>       <GROUP>         56       3912     291860   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       110          0          3          0          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        72          0          0          0          0         96   _c16rtomb.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1052          0          0          0          0        148   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
       884          6          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        22          0          0          0          0         80   abort.o
       128         42          0          0          0         76   assert.o
        20          0          0          0          0         76   assert_puts.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        668   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       240          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       124          0          0          0          0         88   strcmpv7em.o
       150          0          0          0          0         80   strncmp.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        60          4          0          0          0         80   vsnprintf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        26          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      9548        <USER>        <GROUP>          0         96       6320   Library Totals
        18          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9210        362        551          0         96       5468   c_w.l
       272          8          0          0          0        728   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      9548        <USER>        <GROUP>          0         96       6320   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     28392       1282       2208         56       4008     294200   Grand Totals
     28392       1282       2208         56       4008     294200   ELF Image Totals
     28392       1282       2208         56          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30600 (  29.88kB)
    Total RW  Size (RW Data + ZI Data)              4064 (   3.97kB)
    Total ROM Size (Code + RO Data + RW Data)      30656 (  29.94kB)

==============================================================================

