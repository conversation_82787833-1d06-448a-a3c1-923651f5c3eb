#include "pi_bsp.h"

// 默认值设为无效状态，X, Y 为 0
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

// MaixCam 数据解析函数，解析格式：$$R,x,y##
// 这个函数解析红色激光坐标数据并更新全局变量 latest_red_laser_coord
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针检查

    int parsed_x, parsed_y; // 临时变量用于存储解析出的X,Y坐标
    int parsed_count;

    // 尝试匹配 "$$R,x,y##" 格式
    if (strncmp(buffer, "$$R,", 4) == 0)
    {
        // 查找结束标记 "##"
        char *end_marker = strstr(buffer, "##");
        if (!end_marker)
        {
            return -4; // 没有找到结束标记
        }

        // 解析坐标数据：$$R,x,y##
        parsed_count = sscanf(buffer, "$$R,%d,%d##", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2; // 解析失败

        // 解析成功，更新全局红色激光坐标
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1; // 标记数据为有效

        // 打印调试信息
        my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    else
    {
        // 不是预期的格式，认为是未知格式或无效数据
        return -3; // 未知或无效格式
    }

    return 0; // 成功
}



void pi_proc(void)
{
	float pos_out_x, pos_out_y = 0;

	// 检查红色激光坐标数据是否有效
	if (latest_red_laser_coord.isValid)
	{
		// 定义目标位置（屏幕中心或其他期望位置）
		// 假设屏幕分辨率为640x480，中心点为(320, 240)
		int target_x = 150;
		int target_y = 95;

		// PID控制：红色激光坐标作为当前值，目标位置作为设定值
		pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, target_x, 0);
		pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, target_y, 0);

		// 控制步进电机运动，使红色激光点移动到目标位置
		Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
	}
	else
	{
		// 如果没有有效的激光坐标数据，停止电机
		Step_Motor_Set_Speed_my(0, 0);
	}
}

